<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eHroby - <PERSON><PERSON><PERSON><PERSON> cenov<PERSON>ch pon<PERSON></title>
    <meta name="description" content="Profesionálny generátor PDF cenových ponúk pre služby starostlivosti o hrobové miesta">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // Cenové tabuľky podľa špecifikácie
        const PRICES = {
            jednorazove: {
                urnove: { bezDph: 39.99, sDph: 47.99 },
                jednohrob_nesavy: { bezDph: 49.99, sDph: 59.99 },
                jednohrob_savy: { bezDph: 54.99, sDph: 65.99 },
                dvojhrob_nesavy: { bezDph: 64.99, sDph: 77.99 },
                dvojhrob_savy: { bezDph: 69.99, sDph: 83.99 }
            },
            duo: {
                urnove: { bezDph: 74.99, sDph: 89.99 },
                jednohrob_nesavy: { bezDph: 94.99, sDph: 113.99 },
                jednohrob_savy: { bezDph: 98.99, sDph: 118.79 },
                dvojhrob_nesavy: { bezDph: 119.99, sDph: 143.99 },
                dvojhrob_savy: { bezDph: 124.99, sDph: 149.99 }
            },
            stvrtrocne: {
                urnove: { bezDph: 149.99, sDph: 179.99 },
                jednohrob_nesavy: { bezDph: 189.99, sDph: 227.99 },
                jednohrob_savy: { bezDph: 199.99, sDph: 239.99 },
                dvojhrob_nesavy: { bezDph: 239.99, sDph: 287.99 },
                dvojhrob_savy: { bezDph: 259.99, sDph: 311.99 }
            },
            stvrtrocne_akcia: {
                urnove: { bezDph: 299.98, sDph: 359.98 },
                jednohrob_nesavy: { bezDph: 379.98, sDph: 455.98 },
                jednohrob_savy: { bezDph: 399.98, sDph: 479.98 },
                dvojhrob_nesavy: { bezDph: 479.98, sDph: 575.98 },
                dvojhrob_savy: { bezDph: 519.98, sDph: 623.98 }
            },
            mesacne: {
                urnove: { bezDph: 329.99, sDph: 395.99 },
                jednohrob_nesavy: { bezDph: 429.99, sDph: 515.99 },
                jednohrob_savy: { bezDph: 459.99, sDph: 551.99 },
                dvojhrob_nesavy: { bezDph: 549.99, sDph: 659.99 },
                dvojhrob_savy: { bezDph: 589.99, sDph: 707.99 }
            },
            specialna: {
                urnove: { bezDph: 659.98, sDph: 791.98 },
                jednohrob_nesavy: { bezDph: 859.98, sDph: 1031.98 },
                jednohrob_savy: { bezDph: 919.98, sDph: 1103.98 },
                dvojhrob_nesavy: { bezDph: 1099.98, sDph: 1319.98 },
                dvojhrob_savy: { bezDph: 1179.98, sDph: 1415.98 }
            }
        };

        const GRAVE_TYPES = {
            urnove: 'Urnové miesto',
            jednohrob_nesavy: 'Jednohrob (nesavý)',
            jednohrob_savy: 'Jednohrob (savý)',
            dvojhrob_nesavy: 'Dvojhrob (nesavý)',
            dvojhrob_savy: 'Dvojhrob (savý)'
        };

        const SERVICE_TYPES = {
            jednorazove: 'Jednorazové umytie (1× ročne)',
            duo: 'DUO umytie (2× ročne)',
            stvrtrocne: 'Štvrťročná starostlivosť (4× ročne)',
            stvrtrocne_akcia: 'Štvrťročná starostlivosť 2+1 rok zadarmo (3 roky)',
            mesacne: 'Každý mesiac (12× ročne)',
            specialna: 'Špeciálna ponuka 2+1 zdarma (3 roky)'
        };

        function App() {
            const [showDph, setShowDph] = useState(true);
            const [clientData, setClientData] = useState({
                name: '',
                phone: '',
                email: '',
                address: ''
            });
            const [graves, setGraves] = useState([{
                id: 1,
                type: 'urnove',
                service: 'jednorazove',
                location: ''
            }]);
            const [additionalServices, setAdditionalServices] = useState({
                sviatocne: false,
                pisma: false,
                impregnacia: false
            });
            const [notes, setNotes] = useState('');
            const [discount, setDiscount] = useState(0);

            const addGrave = () => {
                const newId = Math.max(...graves.map(g => g.id)) + 1;
                setGraves([...graves, {
                    id: newId,
                    type: 'urnove',
                    service: 'jednorazove',
                    location: ''
                }]);
            };

            const removeGrave = (id) => {
                if (graves.length > 1) {
                    setGraves(graves.filter(g => g.id !== id));
                }
            };

            const updateGrave = (id, field, value) => {
                setGraves(graves.map(g => 
                    g.id === id ? { ...g, [field]: value } : g
                ));
            };

            const calculateSubtotal = () => {
                let total = 0;
                graves.forEach(grave => {
                    const price = PRICES[grave.service][grave.type];
                    total += showDph ? price.sDph : price.bezDph;
                });

                // Doplnkové služby
                if (additionalServices.sviatocne) total += showDph ? 59.99 : 49.99;
                if (additionalServices.pisma) total += showDph ? 119.99 : 99.99;
                if (additionalServices.impregnacia) total += showDph ? 71.99 : 59.99;

                return total;
            };

            const calculateTotal = () => {
                const subtotal = calculateSubtotal();
                const discountAmount = subtotal * (discount / 100);
                return subtotal - discountAmount;
            };

            const generatePDF = async () => {
                console.log('generatePDF called');

                // Validate required fields
                if (!clientData.name || !clientData.phone) {
                    alert('Prosím vyplňte meno a telefón zákazníka.');
                    return;
                }

                if (graves.length === 0) {
                    alert('Prosím pridajte aspoň jeden hrob.');
                    return;
                }

                // Check if jsPDF is available
                if (typeof window.jsPDF === 'undefined') {
                    alert('Chyba: jsPDF knižnica nie je načítaná. Skúste obnoviť stránku.');
                    return;
                }

                try {
                    // Show loading message
                    const button = document.querySelector('.btn-generate');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i className="fas fa-spinner fa-spin"></i> Generujem PDF...';
                    button.disabled = true;

                    // Create PDF using jsPDF
                    const { jsPDF } = window.jsPDF;
                    const doc = new jsPDF();

                    // Set font for Slovak characters
                    doc.setFont('helvetica');

                    let yPosition = 20;
                    const pageWidth = doc.internal.pageSize.width;
                    const margin = 20;
                    const contentWidth = pageWidth - 2 * margin;

                    // Header
                    doc.setFontSize(24);
                    doc.setTextColor(94, 46, 96); // Primary color
                    doc.text('eHroby', margin, yPosition);

                    doc.setFontSize(12);
                    doc.setTextColor(95, 129, 50); // Secondary color
                    doc.text('vytvárame pokojné spomienky', margin, yPosition + 8);

                    doc.setFontSize(18);
                    doc.setTextColor(50, 120, 129); // Tertiary color
                    doc.text('Cenová ponuka', pageWidth - margin - 60, yPosition);

                    doc.setFontSize(10);
                    doc.setTextColor(100, 100, 100);
                    const today = new Date().toLocaleDateString('sk-SK');
                    doc.text(`Dátum: ${today}`, pageWidth - margin - 60, yPosition + 8);

                    // Line under header
                    yPosition += 15;
                    doc.setDrawColor(94, 46, 96);
                    doc.setLineWidth(1);
                    doc.line(margin, yPosition, pageWidth - margin, yPosition);

                    yPosition += 15;

                    // Contact info
                    doc.setFontSize(12);
                    doc.setTextColor(94, 46, 96);
                    doc.text('Kontakt:', margin, yPosition);
                    yPosition += 6;

                    doc.setFontSize(10);
                    doc.setTextColor(0, 0, 0);
                    doc.text('Vladimír Seman', margin, yPosition);
                    yPosition += 5;
                    doc.text('+421 951 553 464', margin, yPosition);
                    yPosition += 5;
                    doc.text('<EMAIL>', margin, yPosition);
                    yPosition += 15;

                    // Customer info
                    doc.setFontSize(14);
                    doc.setTextColor(94, 46, 96);
                    doc.text('Údaje o zákazníkovi', margin, yPosition);
                    yPosition += 8;

                    doc.setFontSize(11);
                    doc.setTextColor(0, 0, 0);
                    doc.text(`Meno: ${clientData.name}`, margin, yPosition);
                    yPosition += 6;
                    doc.text(`Telefón: ${clientData.phone}`, margin, yPosition);
                    yPosition += 6;

                    if (clientData.email) {
                        doc.text(`Email: ${clientData.email}`, margin, yPosition);
                        yPosition += 6;
                    }

                    if (clientData.address) {
                        doc.text(`Adresa: ${clientData.address}`, margin, yPosition);
                        yPosition += 6;
                    }

                    yPosition += 10;

                    // Services header
                    doc.setFontSize(14);
                    doc.setTextColor(94, 46, 96);
                    doc.text('Vybrané služby', margin, yPosition);
                    yPosition += 10;

                    // Services table
                    doc.setFontSize(10);
                    doc.setTextColor(0, 0, 0);

                    graves.forEach((grave, index) => {
                        const price = PRICES[grave.service][grave.type];
                        const displayPrice = showDph ? price.sDph : price.bezDph;

                        doc.text(`${index + 1}. ${SERVICE_TYPES[grave.service]}`, margin, yPosition);
                        doc.text(`${GRAVE_TYPES[grave.type]}`, margin + 80, yPosition);
                        doc.text(`${displayPrice.toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                        yPosition += 6;

                        if (grave.location) {
                            doc.setTextColor(100, 100, 100);
                            doc.text(`   Lokalita: ${grave.location}`, margin, yPosition);
                            doc.setTextColor(0, 0, 0);
                            yPosition += 6;
                        }
                        yPosition += 2;
                    });

                    // Additional services
                    if (additionalServices.sviatocne || additionalServices.pisma || additionalServices.impregnacia) {
                        yPosition += 5;
                        doc.setFontSize(14);
                        doc.setTextColor(94, 46, 96);
                        doc.text('Doplnkové služby', margin, yPosition);
                        yPosition += 10;

                        doc.setFontSize(10);
                        doc.setTextColor(0, 0, 0);

                        if (additionalServices.sviatocne) {
                            doc.text('Sviatočné čistenie', margin, yPosition);
                            doc.text(`${showDph ? '59,99' : '49,99'} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                            yPosition += 6;
                        }

                        if (additionalServices.pisma) {
                            doc.text('Obnova písma', margin, yPosition);
                            doc.text(`${showDph ? '119,99' : '99,99'} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                            yPosition += 6;
                        }

                        if (additionalServices.impregnacia) {
                            doc.text('Impregnácia kameňa', margin, yPosition);
                            doc.text(`${showDph ? '71,99' : '59,99'} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                            yPosition += 6;
                        }
                    }

                    yPosition += 10;

                    // Price summary
                    doc.setFontSize(14);
                    doc.setTextColor(94, 46, 96);
                    doc.text('Súhrn cien', margin, yPosition);
                    yPosition += 10;

                    doc.setFontSize(11);
                    doc.setTextColor(0, 0, 0);

                    const subtotal = calculateSubtotal();
                    const total = calculateTotal();

                    if (showDph) {
                        doc.text('Základ dane:', margin, yPosition);
                        doc.text(`${(subtotal / 1.2).toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                        yPosition += 6;

                        doc.text('DPH 20%:', margin, yPosition);
                        doc.text(`${(subtotal - subtotal / 1.2).toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                        yPosition += 6;
                    }

                    if (discount > 0) {
                        doc.text(`Zľava ${discount}%:`, margin, yPosition);
                        doc.setTextColor(239, 68, 68); // Red color for discount
                        doc.text(`-${(subtotal * discount / 100).toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                        doc.setTextColor(0, 0, 0);
                        yPosition += 6;
                    }

                    // Total line
                    yPosition += 5;
                    doc.setLineWidth(0.5);
                    doc.line(margin, yPosition, pageWidth - margin, yPosition);
                    yPosition += 8;

                    doc.setFontSize(16);
                    doc.setTextColor(94, 46, 96);
                    doc.text(`Celkom ${showDph ? 's DPH' : 'bez DPH'}:`, margin, yPosition);
                    doc.text(`${total.toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });

                    // Notes
                    if (notes.trim()) {
                        yPosition += 15;
                        doc.setFontSize(12);
                        doc.setTextColor(94, 46, 96);
                        doc.text('Poznámky:', margin, yPosition);
                        yPosition += 8;

                        doc.setFontSize(10);
                        doc.setTextColor(0, 0, 0);
                        const splitNotes = doc.splitTextToSize(notes, contentWidth);
                        doc.text(splitNotes, margin, yPosition);
                    }

                    // Footer
                    const footerY = doc.internal.pageSize.height - 30;
                    doc.setFontSize(10);
                    doc.setTextColor(100, 100, 100);
                    doc.text('Ďakujeme za Váš záujem o naše služby!', pageWidth / 2, footerY, { align: 'center' });
                    doc.text('Pre viac informácií nás kontaktujte na +421 951 553 464 alebo <EMAIL>', pageWidth / 2, footerY + 5, { align: 'center' });
                    doc.text('Ponuka platná 30 dní od dátumu vystavenia', pageWidth / 2, footerY + 10, { align: 'center' });
                    doc.setTextColor(94, 46, 96);
                    doc.text('eHroby - vytvárame pokojné spomienky', pageWidth / 2, footerY + 15, { align: 'center' });

                    // Save PDF
                    const filename = `cenova-ponuka-ehroby-${clientData.name.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.pdf`;
                    doc.save(filename);

                    console.log('PDF generated successfully');
                    alert('PDF bolo úspešne vygenerované!');

                    // Restore button
                    button.innerHTML = originalText;
                    button.disabled = false;

                } catch (error) {
                    console.error('PDF generation error:', error);
                    alert('Chyba pri generovaní PDF. Skúste to znovu alebo obnovte stránku.');

                    // Restore button
                    const button = document.querySelector('.btn-generate');
                    button.innerHTML = '<i className="fas fa-file-pdf"></i> Generovať PDF ponuku';
                    button.disabled = false;
                }
            };



            return (
                <div className="app">
                    <header className="header">
                        <div className="container">
                            <h1><i className="fas fa-file-invoice"></i> eHroby - Generátor cenových ponúk</h1>
                            <p>Profesionálne služby starostlivosti o hrobové miesta</p>
                        </div>
                    </header>

                    <main className="main">
                        <div className="container">
                            <div className="form-section">
                                <h2><i className="fas fa-user"></i> Údaje klienta</h2>
                                <div className="form-grid">
                                    <div className="form-group">
                                        <label>Meno a priezvisko *</label>
                                        <input
                                            type="text"
                                            value={clientData.name}
                                            onChange={(e) => setClientData({...clientData, name: e.target.value})}
                                            placeholder="Zadajte meno a priezvisko"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Telefón *</label>
                                        <input
                                            type="tel"
                                            value={clientData.phone}
                                            onChange={(e) => setClientData({...clientData, phone: e.target.value})}
                                            placeholder="+421 xxx xxx xxx"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Email</label>
                                        <input
                                            type="email"
                                            value={clientData.email}
                                            onChange={(e) => setClientData({...clientData, email: e.target.value})}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Adresa hrobových miest</label>
                                        <input
                                            type="text"
                                            value={clientData.address}
                                            onChange={(e) => setClientData({...clientData, address: e.target.value})}
                                            placeholder="Cintorín, mesto"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="form-section">
                                <div className="section-header">
                                    <h2><i className="fas fa-cross"></i> Výber služieb</h2>
                                    <div className="price-toggle">
                                        <label className="toggle-switch">
                                            <input
                                                type="checkbox"
                                                checked={showDph}
                                                onChange={(e) => setShowDph(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                        <span>Zobraziť ceny {showDph ? 's DPH' : 'bez DPH'}</span>
                                    </div>
                                </div>

                                {graves.map((grave, index) => (
                                    <div key={grave.id} className="grave-item">
                                        <div className="grave-header">
                                            <h3>Hrob #{index + 1}</h3>
                                            {graves.length > 1 && (
                                                <button
                                                    className="btn-remove"
                                                    onClick={() => removeGrave(grave.id)}
                                                >
                                                    <i className="fas fa-trash"></i>
                                                </button>
                                            )}
                                        </div>

                                        <div className="form-grid">
                                            <div className="form-group">
                                                <label>Typ hrobu</label>
                                                <select
                                                    value={grave.type}
                                                    onChange={(e) => updateGrave(grave.id, 'type', e.target.value)}
                                                >
                                                    {Object.entries(GRAVE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Frekvencia služby</label>
                                                <select
                                                    value={grave.service}
                                                    onChange={(e) => updateGrave(grave.id, 'service', e.target.value)}
                                                >
                                                    {Object.entries(SERVICE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Lokalita hrobu</label>
                                                <input
                                                    type="text"
                                                    value={grave.location}
                                                    onChange={(e) => updateGrave(grave.id, 'location', e.target.value)}
                                                    placeholder="Napr. sektor A, rad 5, číslo 12"
                                                />
                                            </div>

                                            <div className="price-display">
                                                <span className="price">
                                                    {showDph
                                                        ? PRICES[grave.service][grave.type].sDph.toFixed(2)
                                                        : PRICES[grave.service][grave.type].bezDph.toFixed(2)
                                                    } €
                                                </span>
                                                <small>{showDph ? 's DPH' : 'bez DPH'}</small>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                <button className="btn-add" onClick={addGrave}>
                                    <i className="fas fa-plus"></i> Pridať ďalší hrob
                                </button>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-plus-circle"></i> Doplnkové služby</h2>
                                <div className="additional-services">
                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.sviatocne}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                sviatocne: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Sviatočné čistenie</span>
                                        <span className="service-price">
                                            {showDph ? '59,99' : '49,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.pisma}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                pisma: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Obnova písma</span>
                                        <span className="service-price">
                                            {showDph ? '119,99' : '99,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.impregnacia}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                impregnacia: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Impregnácia kameňa</span>
                                        <span className="service-price">
                                            {showDph ? '71,99' : '59,99'} €
                                        </span>
                                    </label>
                                </div>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-percentage"></i> Zľava</h2>
                                <div className="discount-section">
                                    <label className="form-group">
                                        <span>Zľava (%)</span>
                                        <select
                                            value={discount}
                                            onChange={(e) => setDiscount(Number(e.target.value))}
                                        >
                                            <option value={0}>Bez zľavy</option>
                                            <option value={5}>5% zľava</option>
                                            <option value={10}>10% zľava</option>
                                            <option value={15}>15% zľava</option>
                                        </select>
                                    </label>
                                </div>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-sticky-note"></i> Poznámky</h2>
                                <textarea
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Špeciálne požiadavky alebo poznámky..."
                                    rows="4"
                                ></textarea>
                            </div>

                            <div className="summary-section">
                                <div className="total-price">
                                    <div className="price-breakdown">
                                        <div className="subtotal">
                                            <span>Medzisúčet:</span>
                                            <span>{calculateSubtotal().toFixed(2)} €</span>
                                        </div>
                                        {discount > 0 && (
                                            <div className="discount-amount">
                                                <span>Zľava ({discount}%):</span>
                                                <span className="discount-value">-{(calculateSubtotal() * discount / 100).toFixed(2)} €</span>
                                            </div>
                                        )}
                                        <div className="total">
                                            <h2>Celková suma: {calculateTotal().toFixed(2)} €</h2>
                                            <p>{showDph ? 'Cena s DPH (20%)' : 'Cena bez DPH'}</p>
                                        </div>
                                    </div>

                                    {showDph && (
                                        <div className="tax-breakdown">
                                            <div>Základ dane: {(calculateTotal() / 1.2).toFixed(2)} €</div>
                                            <div>DPH (20%): {(calculateTotal() - calculateTotal() / 1.2).toFixed(2)} €</div>
                                        </div>
                                    )}
                                </div>

                                <button
                                    className="btn-generate"
                                    onClick={generatePDF}
                                    disabled={!clientData.name || !clientData.phone}
                                >
                                    <i className="fas fa-file-pdf"></i> Generovať PDF ponuku
                                </button>
                            </div>
                        </div>
                    </main>

                    <footer className="footer">
                        <div className="container">
                            <p>&copy; 2024 eHroby - Vladimír Seman | +421 951 553 464 | <EMAIL></p>
                        </div>
                    </footer>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
