<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eHroby - <PERSON>r<PERSON><PERSON> cenov<PERSON>ch pon<PERSON>k</title>
    <meta name="description" content="Profesionálny generátor PDF cenových ponúk pre služby starostlivosti o hrobové miesta">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="jspdf.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // Cenové tabuľky podľa špecifikácie
        const PRICES = {
            jednorazove: {
                urnove: { bezDph: 39.99, sDph: 47.99 },
                jednohrob_nesavy: { bezDph: 49.99, sDph: 59.99 },
                jednohrob_savy: { bezDph: 54.99, sDph: 65.99 },
                dvojhrob_nesavy: { bezDph: 64.99, sDph: 77.99 },
                dvojhrob_savy: { bezDph: 69.99, sDph: 83.99 }
            },
            duo: {
                urnove: { bezDph: 74.99, sDph: 89.99 },
                jednohrob_nesavy: { bezDph: 94.99, sDph: 113.99 },
                jednohrob_savy: { bezDph: 98.99, sDph: 118.79 },
                dvojhrob_nesavy: { bezDph: 119.99, sDph: 143.99 },
                dvojhrob_savy: { bezDph: 124.99, sDph: 149.99 }
            },
            stvrtrocne: {
                urnove: { bezDph: 149.99, sDph: 179.99 },
                jednohrob_nesavy: { bezDph: 189.99, sDph: 227.99 },
                jednohrob_savy: { bezDph: 199.99, sDph: 239.99 },
                dvojhrob_nesavy: { bezDph: 239.99, sDph: 287.99 },
                dvojhrob_savy: { bezDph: 259.99, sDph: 311.99 }
            },
            stvrtrocne_akcia: {
                urnove: { bezDph: 299.98, sDph: 359.98 },
                jednohrob_nesavy: { bezDph: 379.98, sDph: 455.98 },
                jednohrob_savy: { bezDph: 399.98, sDph: 479.98 },
                dvojhrob_nesavy: { bezDph: 479.98, sDph: 575.98 },
                dvojhrob_savy: { bezDph: 519.98, sDph: 623.98 }
            },
            mesacne: {
                urnove: { bezDph: 329.99, sDph: 395.99 },
                jednohrob_nesavy: { bezDph: 429.99, sDph: 515.99 },
                jednohrob_savy: { bezDph: 459.99, sDph: 551.99 },
                dvojhrob_nesavy: { bezDph: 549.99, sDph: 659.99 },
                dvojhrob_savy: { bezDph: 589.99, sDph: 707.99 }
            },
            specialna: {
                urnove: { bezDph: 659.98, sDph: 791.98 },
                jednohrob_nesavy: { bezDph: 859.98, sDph: 1031.98 },
                jednohrob_savy: { bezDph: 919.98, sDph: 1103.98 },
                dvojhrob_nesavy: { bezDph: 1099.98, sDph: 1319.98 },
                dvojhrob_savy: { bezDph: 1179.98, sDph: 1415.98 }
            }
        };

        const GRAVE_TYPES = {
            urnove: 'Urnové miesto',
            jednohrob_nesavy: 'Jednohrob (nesavý)',
            jednohrob_savy: 'Jednohrob (savý)',
            dvojhrob_nesavy: 'Dvojhrob (nesavý)',
            dvojhrob_savy: 'Dvojhrob (savý)'
        };

        const GRAVE_TYPES_PDF = {
            urnove: 'Urnove miesto',
            jednohrob_nesavy: 'Jednohrob (nesavy)',
            jednohrob_savy: 'Jednohrob (savy)',
            dvojhrob_nesavy: 'Dvojhrob (nesavy)',
            dvojhrob_savy: 'Dvojhrob (savy)'
        };

        const SERVICE_TYPES = {
            jednorazove: 'Jednorazové umytie (1× ročne)',
            duo: 'DUO umytie (2× ročne)',
            stvrtrocne: 'Štvrťročná starostlivosť (4× ročne)',
            stvrtrocne_akcia: 'Štvrťročná starostlivosť 2+1 rok zadarmo (3 roky)',
            mesacne: 'Každý mesiac (12× ročne)',
            specialna: 'Špeciálna ponuka 2+1 zdarma (3 roky)'
        };

        const SERVICE_TYPES_PDF = {
            jednorazove: 'Jednorazove umytie (1x rocne)',
            duo: 'DUO umytie (2x rocne)',
            stvrtrocne: 'Stvrtrocna starostlivost (4x rocne)',
            stvrtrocne_akcia: 'Stvrtrocna starostlivost 2+1 rok zadarmo (3 roky)',
            mesacne: 'Kazdy mesiac (12x rocne)',
            specialna: 'Specialna ponuka 2+1 zdarma (3 roky)'
        };

        function App() {
            const [showDph, setShowDph] = useState(true);
            const [clientData, setClientData] = useState({
                name: '',
                phone: '',
                email: '',
                address: ''
            });
            const [graves, setGraves] = useState([{
                id: 1,
                type: 'urnove',
                service: 'jednorazove',
                location: ''
            }]);
            const [additionalServices, setAdditionalServices] = useState({
                sviatocne: false,
                pisma: false,
                impregnacia: false
            });
            const [notes, setNotes] = useState('');
            const [discount, setDiscount] = useState(0);

            const addGrave = () => {
                const newId = Math.max(...graves.map(g => g.id)) + 1;
                setGraves([...graves, {
                    id: newId,
                    type: 'urnove',
                    service: 'jednorazove',
                    location: ''
                }]);
            };

            const removeGrave = (id) => {
                if (graves.length > 1) {
                    setGraves(graves.filter(g => g.id !== id));
                }
            };

            const updateGrave = (id, field, value) => {
                setGraves(graves.map(g => 
                    g.id === id ? { ...g, [field]: value } : g
                ));
            };

            const calculateSubtotal = () => {
                let total = 0;
                graves.forEach(grave => {
                    const price = PRICES[grave.service][grave.type];
                    total += showDph ? price.sDph : price.bezDph;
                });

                // Doplnkové služby
                if (additionalServices.sviatocne) total += showDph ? 59.99 : 49.99;
                if (additionalServices.pisma) total += showDph ? 119.99 : 99.99;
                if (additionalServices.impregnacia) total += showDph ? 71.99 : 59.99;

                return total;
            };

            const calculateTotal = () => {
                const subtotal = calculateSubtotal();
                const discountAmount = subtotal * (discount / 100);
                return subtotal - discountAmount;
            };

            const generatePDF = async () => {
                console.log('generatePDF called');

                // Validate required fields
                if (!clientData.name || !clientData.phone) {
                    alert('Prosím vyplňte meno a telefón zákazníka.');
                    return;
                }

                if (graves.length === 0) {
                    alert('Prosím pridajte aspoň jeden hrob.');
                    return;
                }

                // Check if jsPDF is available
                console.log('Checking jsPDF availability...');
                console.log('typeof jsPDF:', typeof jsPDF);
                console.log('typeof window.jsPDF:', typeof window.jsPDF);
                console.log('window.jspdf:', typeof window.jspdf);

                let jsPDFConstructor = null;

                if (typeof jsPDF !== 'undefined') {
                    jsPDFConstructor = jsPDF;
                    console.log('Using global jsPDF');
                } else if (typeof window.jsPDF !== 'undefined') {
                    jsPDFConstructor = window.jsPDF;
                    console.log('Using window.jsPDF');
                } else if (window.jspdf && window.jspdf.jsPDF) {
                    jsPDFConstructor = window.jspdf.jsPDF;
                    console.log('Using window.jspdf.jsPDF');
                } else {
                    // Fallback to print dialog
                    if (confirm('jsPDF knižnica nie je dostupná. Chcete použiť tlačový dialóg namiesto toho?')) {
                        generatePrintVersion();
                        return;
                    } else {
                        alert('Chyba: jsPDF knižnica nie je načítaná. Skúste obnoviť stránku.');
                        return;
                    }
                }

                try {
                    // Show loading message
                    const button = document.querySelector('.btn-generate');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i className="fas fa-spinner fa-spin"></i> Generujem PDF...';
                    button.disabled = true;

                    // Create PDF using jsPDF
                    const doc = new jsPDFConstructor();

                    // Set font for Slovak characters - use courier for better diacritic support
                    doc.setFont('courier');

                    // Function to convert Slovak characters for better PDF compatibility
                    const convertSlovakText = (text) => {
                        if (!text) return '';
                        // Convert problematic characters to ASCII equivalents as fallback
                        return text
                            .replace(/á/g, 'a').replace(/Á/g, 'A')
                            .replace(/ä/g, 'a').replace(/Ä/g, 'A')
                            .replace(/č/g, 'c').replace(/Č/g, 'C')
                            .replace(/ď/g, 'd').replace(/Ď/g, 'D')
                            .replace(/é/g, 'e').replace(/É/g, 'E')
                            .replace(/í/g, 'i').replace(/Í/g, 'I')
                            .replace(/ľ/g, 'l').replace(/Ľ/g, 'L')
                            .replace(/ĺ/g, 'l').replace(/Ĺ/g, 'L')
                            .replace(/ň/g, 'n').replace(/Ň/g, 'N')
                            .replace(/ó/g, 'o').replace(/Ó/g, 'O')
                            .replace(/ô/g, 'o').replace(/Ô/g, 'O')
                            .replace(/ŕ/g, 'r').replace(/Ŕ/g, 'R')
                            .replace(/š/g, 's').replace(/Š/g, 'S')
                            .replace(/ť/g, 't').replace(/Ť/g, 'T')
                            .replace(/ú/g, 'u').replace(/Ú/g, 'U')
                            .replace(/ý/g, 'y').replace(/Ý/g, 'Y')
                            .replace(/ž/g, 'z').replace(/Ž/g, 'Z');
                    };

                    let yPosition = 20;
                    const pageWidth = doc.internal.pageSize.width;
                    const margin = 20;
                    const contentWidth = pageWidth - 2 * margin;

                    // Header
                    doc.setFontSize(24);
                    doc.setTextColor(94, 46, 96); // Primary color
                    doc.text('eHroby', margin, yPosition);

                    doc.setFontSize(12);
                    doc.setTextColor(95, 129, 50); // Secondary color
                    doc.text(convertSlovakText('vytvárame pokojné spomienky'), margin, yPosition + 8);

                    doc.setFontSize(18);
                    doc.setTextColor(50, 120, 129); // Tertiary color
                    doc.text(convertSlovakText('Cenová ponuka'), pageWidth - margin - 60, yPosition);

                    doc.setFontSize(10);
                    doc.setTextColor(100, 100, 100);
                    const today = new Date().toLocaleDateString('sk-SK');
                    doc.text(convertSlovakText(`Datum: ${today}`), pageWidth - margin - 60, yPosition + 8);

                    // Line under header
                    yPosition += 15;
                    doc.setDrawColor(94, 46, 96);
                    doc.setLineWidth(1);
                    doc.line(margin, yPosition, pageWidth - margin, yPosition);

                    yPosition += 15;

                    // Contact info
                    doc.setFontSize(12);
                    doc.setTextColor(94, 46, 96);
                    doc.text('Kontakt:', margin, yPosition);
                    yPosition += 6;

                    doc.setFontSize(10);
                    doc.setTextColor(0, 0, 0);
                    doc.text(convertSlovakText('Vladimir Seman'), margin, yPosition);
                    yPosition += 5;
                    doc.text('+421 951 553 464', margin, yPosition);
                    yPosition += 5;
                    doc.text('<EMAIL>', margin, yPosition);
                    yPosition += 15;

                    // Customer info
                    doc.setFontSize(14);
                    doc.setTextColor(94, 46, 96);
                    doc.text(convertSlovakText('Udaje o zakaznikovi'), margin, yPosition);
                    yPosition += 8;

                    doc.setFontSize(11);
                    doc.setTextColor(0, 0, 0);
                    doc.text(convertSlovakText(`Meno: ${clientData.name}`), margin, yPosition);
                    yPosition += 6;
                    doc.text(convertSlovakText(`Telefon: ${clientData.phone}`), margin, yPosition);
                    yPosition += 6;

                    if (clientData.email) {
                        doc.text(convertSlovakText(`Email: ${clientData.email}`), margin, yPosition);
                        yPosition += 6;
                    }

                    if (clientData.address) {
                        doc.text(convertSlovakText(`Adresa: ${clientData.address}`), margin, yPosition);
                        yPosition += 6;
                    }

                    yPosition += 10;

                    // Services header
                    doc.setFontSize(14);
                    doc.setTextColor(94, 46, 96);
                    doc.text(convertSlovakText('Vybrane sluzby'), margin, yPosition);
                    yPosition += 10;

                    // Services table
                    doc.setFontSize(10);
                    doc.setTextColor(0, 0, 0);

                    graves.forEach((grave, index) => {
                        const price = PRICES[grave.service][grave.type];
                        const displayPrice = showDph ? price.sDph : price.bezDph;

                        doc.text(`${index + 1}. ${SERVICE_TYPES_PDF[grave.service]}`, margin, yPosition);
                        doc.text(`${GRAVE_TYPES_PDF[grave.type]}`, margin + 80, yPosition);
                        doc.text(`${displayPrice.toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                        yPosition += 6;

                        if (grave.location) {
                            doc.setTextColor(100, 100, 100);
                            doc.text(convertSlovakText(`   Lokalita: ${grave.location}`), margin, yPosition);
                            doc.setTextColor(0, 0, 0);
                            yPosition += 6;
                        }
                        yPosition += 2;
                    });

                    // Additional services
                    if (additionalServices.sviatocne || additionalServices.pisma || additionalServices.impregnacia) {
                        yPosition += 5;
                        doc.setFontSize(14);
                        doc.setTextColor(94, 46, 96);
                        doc.text(convertSlovakText('Doplnkove sluzby'), margin, yPosition);
                        yPosition += 10;

                        doc.setFontSize(10);
                        doc.setTextColor(0, 0, 0);

                        if (additionalServices.sviatocne) {
                            doc.text(convertSlovakText('Sviatocne cistenie'), margin, yPosition);
                            doc.text(`${showDph ? '59,99' : '49,99'} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                            yPosition += 6;
                        }

                        if (additionalServices.pisma) {
                            doc.text(convertSlovakText('Obnova pisma'), margin, yPosition);
                            doc.text(`${showDph ? '119,99' : '99,99'} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                            yPosition += 6;
                        }

                        if (additionalServices.impregnacia) {
                            doc.text(convertSlovakText('Impregnacia kamena'), margin, yPosition);
                            doc.text(`${showDph ? '71,99' : '59,99'} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                            yPosition += 6;
                        }
                    }

                    yPosition += 10;

                    // Price summary
                    doc.setFontSize(14);
                    doc.setTextColor(94, 46, 96);
                    doc.text(convertSlovakText('Suhrn cien'), margin, yPosition);
                    yPosition += 10;

                    doc.setFontSize(11);
                    doc.setTextColor(0, 0, 0);

                    const subtotal = calculateSubtotal();
                    const total = calculateTotal();

                    if (showDph) {
                        doc.text(convertSlovakText('Zaklad dane:'), margin, yPosition);
                        doc.text(`${(subtotal / 1.2).toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                        yPosition += 6;

                        doc.text('DPH 20%:', margin, yPosition);
                        doc.text(`${(subtotal - subtotal / 1.2).toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                        yPosition += 6;
                    }

                    if (discount > 0) {
                        doc.text(convertSlovakText(`Zlava ${discount}%:`), margin, yPosition);
                        doc.setTextColor(239, 68, 68); // Red color for discount
                        doc.text(`-${(subtotal * discount / 100).toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });
                        doc.setTextColor(0, 0, 0);
                        yPosition += 6;
                    }

                    // Total line
                    yPosition += 5;
                    doc.setLineWidth(0.5);
                    doc.line(margin, yPosition, pageWidth - margin, yPosition);
                    yPosition += 8;

                    doc.setFontSize(16);
                    doc.setTextColor(94, 46, 96);
                    doc.text(convertSlovakText(`Celkom ${showDph ? 's DPH' : 'bez DPH'}:`), margin, yPosition);
                    doc.text(`${total.toFixed(2)} €`, pageWidth - margin - 30, yPosition, { align: 'right' });

                    // Notes
                    if (notes.trim()) {
                        yPosition += 15;
                        doc.setFontSize(12);
                        doc.setTextColor(94, 46, 96);
                        doc.text(convertSlovakText('Poznamky:'), margin, yPosition);
                        yPosition += 8;

                        doc.setFontSize(10);
                        doc.setTextColor(0, 0, 0);
                        const splitNotes = doc.splitTextToSize(convertSlovakText(notes), contentWidth);
                        doc.text(splitNotes, margin, yPosition);
                    }

                    // Footer
                    const footerY = doc.internal.pageSize.height - 30;
                    doc.setFontSize(10);
                    doc.setTextColor(100, 100, 100);
                    doc.text(convertSlovakText('Dakujeme za Vas zaujem o nase sluzby!'), pageWidth / 2, footerY, { align: 'center' });
                    doc.text(convertSlovakText('Pre viac informacii nas kontaktujte na +421 951 553 464 alebo <EMAIL>'), pageWidth / 2, footerY + 5, { align: 'center' });
                    doc.text(convertSlovakText('Ponuka platna 30 dni od datumu vystavenia'), pageWidth / 2, footerY + 10, { align: 'center' });
                    doc.setTextColor(94, 46, 96);
                    doc.text(convertSlovakText('eHroby - vytvarame pokojne spomienky'), pageWidth / 2, footerY + 15, { align: 'center' });

                    // Save PDF
                    const filename = `cenova-ponuka-ehroby-${clientData.name.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.pdf`;
                    doc.save(filename);

                    console.log('PDF generated successfully');
                    alert('PDF bolo úspešne vygenerované!');

                    // Restore button
                    button.innerHTML = originalText;
                    button.disabled = false;

                } catch (error) {
                    console.error('PDF generation error:', error);
                    alert('Chyba pri generovaní PDF. Skúste to znovu alebo obnovte stránku.');

                    // Restore button
                    const button = document.querySelector('.btn-generate');
                    button.innerHTML = '<i className="fas fa-file-pdf"></i> Generovať PDF ponuku';
                    button.disabled = false;
                }
            };

            const generatePrintVersion = () => {
                // Create print-friendly content
                const today = new Date().toLocaleDateString('sk-SK');
                const subtotal = calculateSubtotal();
                const total = calculateTotal();

                let printContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Cenová ponuka - eHroby</title>
                    <style>
                        @media print {
                            body { margin: 0; }
                            @page { margin: 0.5in; }
                        }
                        body {
                            font-family: Arial, sans-serif;
                            line-height: 1.4;
                            color: #000;
                            max-width: 800px;
                            margin: 0 auto;
                            padding: 20px;
                        }
                        .header {
                            border-bottom: 3px solid #5e2e60;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .header h1 {
                            color: #5e2e60;
                            font-size: 28px;
                            margin: 0;
                        }
                        .header p {
                            color: #5f8132;
                            margin: 5px 0;
                        }
                        .section {
                            margin-bottom: 25px;
                        }
                        .section h3 {
                            color: #5e2e60;
                            font-size: 16px;
                            margin-bottom: 10px;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-bottom: 15px;
                        }
                        th, td {
                            padding: 8px;
                            text-align: left;
                            border-bottom: 1px solid #ddd;
                        }
                        th {
                            background: #5e2e60;
                            color: white;
                        }
                        .total {
                            font-size: 18px;
                            font-weight: bold;
                            color: #5e2e60;
                            text-align: right;
                            margin-top: 20px;
                        }
                        .footer {
                            text-align: center;
                            margin-top: 40px;
                            padding-top: 20px;
                            border-top: 2px solid #ddd;
                            font-size: 12px;
                            color: #666;
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>eHroby</h1>
                        <p>vytvárame pokojné spomienky</p>
                        <h2 style="color: #327881; float: right; margin: 0;">Cenová ponuka</h2>
                        <p style="float: right; margin: 0;">Dátum: ${today}</p>
                        <div style="clear: both;"></div>
                    </div>

                    <div class="section">
                        <h3>Kontakt</h3>
                        <p><strong>Vladimír Seman</strong><br>
                        +421 951 553 464<br>
                        <EMAIL></p>
                    </div>

                    <div class="section">
                        <h3>Údaje o zákazníkovi</h3>
                        <p><strong>Meno:</strong> ${clientData.name}<br>
                        <strong>Telefón:</strong> ${clientData.phone}`;

                if (clientData.email) {
                    printContent += `<br><strong>Email:</strong> ${clientData.email}`;
                }
                if (clientData.address) {
                    printContent += `<br><strong>Adresa:</strong> ${clientData.address}`;
                }

                printContent += `</p>
                    </div>

                    <div class="section">
                        <h3>Vybrané služby</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Služba</th>
                                    <th>Typ hrobu</th>
                                    <th style="text-align: right;">Cena</th>
                                </tr>
                            </thead>
                            <tbody>`;

                graves.forEach((grave, index) => {
                    const price = PRICES[grave.service][grave.type];
                    const displayPrice = showDph ? price.sDph : price.bezDph;

                    printContent += `
                                <tr>
                                    <td>${SERVICE_TYPES[grave.service]}</td>
                                    <td>${GRAVE_TYPES[grave.type]}</td>
                                    <td style="text-align: right;">${displayPrice.toFixed(2)} €</td>
                                </tr>`;

                    if (grave.location) {
                        printContent += `
                                <tr>
                                    <td colspan="3" style="font-style: italic; color: #666; padding-left: 20px;">
                                        📍 Lokalita: ${grave.location}
                                    </td>
                                </tr>`;
                    }
                });

                printContent += `
                            </tbody>
                        </table>
                    </div>`;

                // Additional services
                if (additionalServices.sviatocne || additionalServices.pisma || additionalServices.impregnacia) {
                    printContent += `
                    <div class="section">
                        <h3>Doplnkové služby</h3>
                        <table>
                            <tbody>`;

                    if (additionalServices.sviatocne) {
                        printContent += `
                                <tr>
                                    <td>🎄 Sviatočné čistenie</td>
                                    <td style="text-align: right;">${showDph ? '59,99' : '49,99'} €</td>
                                </tr>`;
                    }

                    if (additionalServices.pisma) {
                        printContent += `
                                <tr>
                                    <td>✍️ Obnova písma</td>
                                    <td style="text-align: right;">${showDph ? '119,99' : '99,99'} €</td>
                                </tr>`;
                    }

                    if (additionalServices.impregnacia) {
                        printContent += `
                                <tr>
                                    <td>🛡️ Impregnácia kameňa</td>
                                    <td style="text-align: right;">${showDph ? '71,99' : '59,99'} €</td>
                                </tr>`;
                    }

                    printContent += `
                            </tbody>
                        </table>
                    </div>`;
                }

                // Price summary
                printContent += `
                    <div class="section">
                        <h3>Súhrn cien</h3>
                        <table>`;

                if (showDph) {
                    printContent += `
                            <tr>
                                <td>Základ dane:</td>
                                <td style="text-align: right;">${(subtotal / 1.2).toFixed(2)} €</td>
                            </tr>
                            <tr>
                                <td>DPH 20%:</td>
                                <td style="text-align: right;">${(subtotal - subtotal / 1.2).toFixed(2)} €</td>
                            </tr>`;
                }

                if (discount > 0) {
                    printContent += `
                            <tr>
                                <td>Zľava ${discount}%:</td>
                                <td style="text-align: right; color: red;">-${(subtotal * discount / 100).toFixed(2)} €</td>
                            </tr>`;
                }

                printContent += `
                            <tr style="font-weight: bold; font-size: 16px;">
                                <td>Celkom ${showDph ? 's DPH' : 'bez DPH'}:</td>
                                <td style="text-align: right;">${total.toFixed(2)} €</td>
                            </tr>
                        </table>
                    </div>`;

                // Notes
                if (notes.trim()) {
                    printContent += `
                    <div class="section">
                        <h3>📝 Poznámky</h3>
                        <p>${notes}</p>
                    </div>`;
                }

                printContent += `
                    <div class="footer">
                        <p><strong>🙏 Ďakujeme za Váš záujem o naše služby!</strong></p>
                        <p>Pre viac informácií nás kontaktujte na <strong>+421 951 553 464</strong> alebo <strong><EMAIL></strong></p>
                        <p style="font-style: italic;">Ponuka platná 30 dní od dátumu vystavenia</p>
                        <p><strong>eHroby - vytvárame pokojné spomienky</strong></p>
                    </div>
                </body>
                </html>`;

                // Open new window with print-friendly content
                const printWindow = window.open('', '_blank');
                printWindow.document.write(printContent);
                printWindow.document.close();

                // Auto-print after a short delay
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            };



            return (
                <div className="app">
                    <header className="header">
                        <div className="container">
                            <h1><i className="fas fa-file-invoice"></i> eHroby - Generátor cenových ponúk</h1>
                            <p>Profesionálne služby starostlivosti o hrobové miesta</p>
                        </div>
                    </header>

                    <main className="main">
                        <div className="container">
                            <div className="form-section">
                                <h2><i className="fas fa-user"></i> Údaje klienta</h2>
                                <div className="form-grid">
                                    <div className="form-group">
                                        <label>Meno a priezvisko *</label>
                                        <input
                                            type="text"
                                            value={clientData.name}
                                            onChange={(e) => setClientData({...clientData, name: e.target.value})}
                                            placeholder="Zadajte meno a priezvisko"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Telefón *</label>
                                        <input
                                            type="tel"
                                            value={clientData.phone}
                                            onChange={(e) => setClientData({...clientData, phone: e.target.value})}
                                            placeholder="+421 xxx xxx xxx"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Email</label>
                                        <input
                                            type="email"
                                            value={clientData.email}
                                            onChange={(e) => setClientData({...clientData, email: e.target.value})}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Adresa hrobových miest</label>
                                        <input
                                            type="text"
                                            value={clientData.address}
                                            onChange={(e) => setClientData({...clientData, address: e.target.value})}
                                            placeholder="Cintorín, mesto"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="form-section">
                                <div className="section-header">
                                    <h2><i className="fas fa-cross"></i> Výber služieb</h2>
                                    <div className="price-toggle">
                                        <label className="toggle-switch">
                                            <input
                                                type="checkbox"
                                                checked={showDph}
                                                onChange={(e) => setShowDph(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                        <span>Zobraziť ceny {showDph ? 's DPH' : 'bez DPH'}</span>
                                    </div>
                                </div>

                                {graves.map((grave, index) => (
                                    <div key={grave.id} className="grave-item">
                                        <div className="grave-header">
                                            <h3>Hrob #{index + 1}</h3>
                                            {graves.length > 1 && (
                                                <button
                                                    className="btn-remove"
                                                    onClick={() => removeGrave(grave.id)}
                                                >
                                                    <i className="fas fa-trash"></i>
                                                </button>
                                            )}
                                        </div>

                                        <div className="form-grid">
                                            <div className="form-group">
                                                <label>Typ hrobu</label>
                                                <select
                                                    value={grave.type}
                                                    onChange={(e) => updateGrave(grave.id, 'type', e.target.value)}
                                                >
                                                    {Object.entries(GRAVE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Frekvencia služby</label>
                                                <select
                                                    value={grave.service}
                                                    onChange={(e) => updateGrave(grave.id, 'service', e.target.value)}
                                                >
                                                    {Object.entries(SERVICE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Lokalita hrobu</label>
                                                <input
                                                    type="text"
                                                    value={grave.location}
                                                    onChange={(e) => updateGrave(grave.id, 'location', e.target.value)}
                                                    placeholder="Napr. sektor A, rad 5, číslo 12"
                                                />
                                            </div>

                                            <div className="price-display">
                                                <span className="price">
                                                    {showDph
                                                        ? PRICES[grave.service][grave.type].sDph.toFixed(2)
                                                        : PRICES[grave.service][grave.type].bezDph.toFixed(2)
                                                    } €
                                                </span>
                                                <small>{showDph ? 's DPH' : 'bez DPH'}</small>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                <button className="btn-add" onClick={addGrave}>
                                    <i className="fas fa-plus"></i> Pridať ďalší hrob
                                </button>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-plus-circle"></i> Doplnkové služby</h2>
                                <div className="additional-services">
                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.sviatocne}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                sviatocne: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Sviatočné čistenie</span>
                                        <span className="service-price">
                                            {showDph ? '59,99' : '49,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.pisma}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                pisma: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Obnova písma</span>
                                        <span className="service-price">
                                            {showDph ? '119,99' : '99,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.impregnacia}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                impregnacia: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Impregnácia kameňa</span>
                                        <span className="service-price">
                                            {showDph ? '71,99' : '59,99'} €
                                        </span>
                                    </label>
                                </div>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-percentage"></i> Zľava</h2>
                                <div className="discount-section">
                                    <label className="form-group">
                                        <span>Zľava (%)</span>
                                        <select
                                            value={discount}
                                            onChange={(e) => setDiscount(Number(e.target.value))}
                                        >
                                            <option value={0}>Bez zľavy</option>
                                            <option value={5}>5% zľava</option>
                                            <option value={10}>10% zľava</option>
                                            <option value={15}>15% zľava</option>
                                        </select>
                                    </label>
                                </div>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-sticky-note"></i> Poznámky</h2>
                                <textarea
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Špeciálne požiadavky alebo poznámky..."
                                    rows="4"
                                ></textarea>
                            </div>

                            <div className="summary-section">
                                <div className="total-price">
                                    <div className="price-breakdown">
                                        <div className="subtotal">
                                            <span>Medzisúčet:</span>
                                            <span>{calculateSubtotal().toFixed(2)} €</span>
                                        </div>
                                        {discount > 0 && (
                                            <div className="discount-amount">
                                                <span>Zľava ({discount}%):</span>
                                                <span className="discount-value">-{(calculateSubtotal() * discount / 100).toFixed(2)} €</span>
                                            </div>
                                        )}
                                        <div className="total">
                                            <h2>Celková suma: {calculateTotal().toFixed(2)} €</h2>
                                            <p>{showDph ? 'Cena s DPH (20%)' : 'Cena bez DPH'}</p>
                                        </div>
                                    </div>

                                    {showDph && (
                                        <div className="tax-breakdown">
                                            <div>Základ dane: {(calculateTotal() / 1.2).toFixed(2)} €</div>
                                            <div>DPH (20%): {(calculateTotal() - calculateTotal() / 1.2).toFixed(2)} €</div>
                                        </div>
                                    )}
                                </div>

                                <button
                                    className="btn-generate"
                                    onClick={generatePDF}
                                    disabled={!clientData.name || !clientData.phone}
                                >
                                    <i className="fas fa-file-pdf"></i> Generovať PDF ponuku
                                </button>
                            </div>
                        </div>
                    </main>

                    <footer className="footer">
                        <div className="container">
                            <p>&copy; 2024 eHroby - Vladimír Seman | +421 951 553 464 | <EMAIL></p>
                        </div>
                    </footer>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
